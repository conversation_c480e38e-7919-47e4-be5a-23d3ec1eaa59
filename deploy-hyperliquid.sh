#!/bin/bash

# Freqtrade Hyperliquid Telegram Bot Deployment Script
# This script helps you deploy Freqtrade with Telegram bot for Hyperliquid exchange

set -e

echo "🚀 Freqtrade Hyperliquid Telegram Bot Deployment"
echo "================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}$1${NC}"
}

print_important() {
    echo -e "${PURPLE}[IMPORTANT]${NC} $1"
}

# Check if Docker is installed
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_status "Docker and Docker Compose are installed ✓"
}

# Check if .env file exists
check_env_file() {
    if [ ! -f .env ]; then
        print_warning ".env file not found!"
        echo ""
        print_header "Setting up environment file..."
        
        if [ -f .env.hyperliquid.template ]; then
            cp .env.hyperliquid.template .env
            print_status "Created .env file from template"
            echo ""
            print_important "🚨 IMPORTANT: You MUST edit the .env file with your actual credentials!"
            echo ""
            echo "Required steps:"
            echo "1. Create API wallet at: https://app.hyperliquid.xyz/API"
            echo "2. Create Telegram bot with @BotFather"
            echo "3. Edit .env file with your credentials"
            echo "4. Run this script again"
            echo ""
            print_warning "Opening .env file for editing..."
            
            # Try to open with common editors
            if command -v code &> /dev/null; then
                code .env
            elif command -v nano &> /dev/null; then
                nano .env
            elif command -v vim &> /dev/null; then
                vim .env
            else
                print_warning "Please edit .env file manually with your preferred editor"
            fi
            
            echo ""
            read -p "Press Enter after you've configured the .env file..."
        else
            print_error ".env.hyperliquid.template not found. Please run the setup script first."
            exit 1
        fi
    fi
    
    print_status ".env file exists ✓"
}

# Validate environment variables
validate_env() {
    print_header "Validating environment configuration..."
    
    source .env
    
    # Check Hyperliquid credentials
    if [[ -z "$HYPERLIQUID_WALLET_ADDRESS" || "$HYPERLIQUID_WALLET_ADDRESS" == "******************************************" ]]; then
        print_error "HYPERLIQUID_WALLET_ADDRESS not configured in .env file"
        exit 1
    fi
    
    if [[ -z "$HYPERLIQUID_PRIVATE_KEY" || "$HYPERLIQUID_PRIVATE_KEY" == "******************************************123456789012345678901234" ]]; then
        print_error "HYPERLIQUID_PRIVATE_KEY not configured in .env file"
        exit 1
    fi
    
    # Check Telegram credentials
    if [[ -z "$TELEGRAM_BOT_TOKEN" || "$TELEGRAM_BOT_TOKEN" == "1234567890:ABCdefGHIjklMNOpqrsTUVwxyz" ]]; then
        print_error "TELEGRAM_BOT_TOKEN not configured in .env file"
        exit 1
    fi
    
    if [[ -z "$TELEGRAM_CHAT_ID" || "$TELEGRAM_CHAT_ID" == "123456789" ]]; then
        print_error "TELEGRAM_CHAT_ID not configured in .env file"
        exit 1
    fi
    
    # Validate wallet address format
    if [[ ! "$HYPERLIQUID_WALLET_ADDRESS" =~ ^0x[a-fA-F0-9]{40}$ ]]; then
        print_error "Invalid wallet address format. Should be 0x followed by 40 hex characters"
        exit 1
    fi
    
    # Validate private key format
    if [[ ! "$HYPERLIQUID_PRIVATE_KEY" =~ ^0x[a-fA-F0-9]{64}$ ]]; then
        print_error "Invalid private key format. Should be 0x followed by 64 hex characters"
        exit 1
    fi
    
    print_status "Environment configuration validated ✓"
}

# Build Docker image
build_image() {
    print_header "Building Docker image..."
    docker-compose -f docker-compose.telegram.yml build
    print_status "Docker image built successfully ✓"
}

# Deploy the bot
deploy_bot() {
    print_header "Deploying Freqtrade Telegram Bot..."
    
    # Stop any existing containers
    docker-compose -f docker-compose.telegram.yml down 2>/dev/null || true
    
    # Start the bot
    docker-compose -f docker-compose.telegram.yml up -d freqtrade-telegram
    
    print_status "Freqtrade Telegram Bot deployed successfully ✓"
    
    # Show status
    echo ""
    print_header "Deployment Status:"
    docker-compose -f docker-compose.telegram.yml ps
    
    echo ""
    print_header "Useful Commands:"
    echo "📊 View logs:           docker-compose -f docker-compose.telegram.yml logs -f"
    echo "🛑 Stop bot:            docker-compose -f docker-compose.telegram.yml down"
    echo "🔄 Restart bot:         docker-compose -f docker-compose.telegram.yml restart"
    echo "📈 Web UI:              http://localhost:8080"
    echo "💬 Telegram commands:   Send /help to your bot"
}

# Show important warnings
show_warnings() {
    echo ""
    print_header "🚨 IMPORTANT SECURITY REMINDERS:"
    echo ""
    print_warning "1. You're starting in DRY RUN mode (no real trades)"
    print_warning "2. Monitor your bot closely when you go live"
    print_warning "3. Start with small amounts"
    print_warning "4. Never share your private keys"
    print_warning "5. Use only API wallet private key (not main wallet)"
    echo ""
    print_important "To enable live trading:"
    print_important "1. Edit user_data/config.telegram.json"
    print_important "2. Change 'dry_run': true to 'dry_run': false"
    print_important "3. Restart the bot"
    echo ""
}

# Main deployment function
main() {
    print_header "🐳 Starting Hyperliquid Telegram Bot Deployment"
    
    check_docker
    check_env_file
    validate_env
    build_image
    deploy_bot
    show_warnings
    
    print_header "🎉 Deployment Complete!"
    echo ""
    print_status "Your Freqtrade Telegram Bot is now running with Hyperliquid!"
    echo ""
    print_status "Next steps:"
    echo "1. Test your bot with Telegram commands"
    echo "2. Monitor the logs for any issues"
    echo "3. When ready, enable live trading (see warnings above)"
    echo ""
    print_status "Happy trading! 🚀"
}

# Handle command line arguments
case "$1" in
    "deploy")
        main
        ;;
    "stop")
        print_header "Stopping Freqtrade Telegram Bot..."
        docker-compose -f docker-compose.telegram.yml down
        print_status "Bot stopped ✓"
        ;;
    "logs")
        print_header "Showing bot logs..."
        docker-compose -f docker-compose.telegram.yml logs -f freqtrade-telegram
        ;;
    "restart")
        print_header "Restarting Freqtrade Telegram Bot..."
        docker-compose -f docker-compose.telegram.yml restart freqtrade-telegram
        print_status "Bot restarted ✓"
        ;;
    "status")
        print_header "Bot Status:"
        docker-compose -f docker-compose.telegram.yml ps
        ;;
    *)
        echo "Freqtrade Hyperliquid Telegram Bot Deployment"
        echo "Usage: $0 {deploy|stop|logs|restart|status}"
        echo ""
        echo "Commands:"
        echo "  deploy   - Deploy the bot (full setup)"
        echo "  stop     - Stop the bot"
        echo "  logs     - Show bot logs"
        echo "  restart  - Restart the bot"
        echo "  status   - Show bot status"
        ;;
esac
