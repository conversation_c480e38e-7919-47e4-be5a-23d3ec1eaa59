# Freqtrade Telegram Bot Environment Configuration
# Copy this file to .env and fill in your actual values

# ===========================================
# EXCHANGE CONFIGURATION
# ===========================================
# Your exchange API credentials
EXCHANGE_API_KEY=your_exchange_api_key_here
EXCHANGE_SECRET_KEY=your_exchange_secret_key_here

# ===========================================
# TELEGRAM BOT CONFIGURATION
# ===========================================
# Get these from @BotFather on Telegram
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_CHAT_ID=your_telegram_chat_id_here

# ===========================================
# API SERVER CONFIGURATION
# ===========================================
# Credentials for FreqUI web interface
API_USERNAME=freqtrader
API_PASSWORD=SuperSecretPassword
JWT_SECRET_KEY=your-secret-jwt-key-change-this-to-something-random

# ===========================================
# TRADING CONFIGURATION
# ===========================================
# Strategy to use
STRATEGY=CustomStrategy

# Timezone (optional)
TZ=UTC

# ===========================================
# BACKTESTING CONFIGURATION (Optional)
# ===========================================
# For backtesting service
TIMERANGE=20231101-20231201
EXCHANGE=binance
PAIRS=BTC/USDT ETH/USDT ADA/USDT
TIMEFRAMES=1h 4h
DAYS=30

# ===========================================
# SECURITY NOTES
# ===========================================
# 1. Never commit the .env file to version control
# 2. Keep your API keys secure
# 3. Use read-only API keys when possible
# 4. Start with dry_run=true for testing
# 5. Change default passwords
