#!/bin/bash

# Configuration Test Script
# Tests your Hyperliquid configuration without requiring Docker

set -e

echo "🧪 Testing Hyperliquid Configuration"
echo "===================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[✓]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[!]${NC} $1"
}

print_error() {
    echo -e "${RED}[✗]${NC} $1"
}

print_header() {
    echo -e "${BLUE}$1${NC}"
}

# Function to validate wallet address
validate_wallet_address() {
    local address=$1
    if [[ ! "$address" =~ ^0x[a-fA-F0-9]{40}$ ]]; then
        return 1
    fi
    return 0
}

# Function to validate private key
validate_private_key() {
    local key=$1
    if [[ ! "$key" =~ ^0x[a-fA-F0-9]{64}$ ]]; then
        return 1
    fi
    return 0
}

# Test configuration
test_config() {
    local errors=0
    
    print_header "📋 Testing Configuration Files"
    echo ""
    
    # Check if .env file exists
    if [ ! -f .env ]; then
        print_error ".env file not found"
        echo "  Run: ./setup-hyperliquid-env.sh setup"
        ((errors++))
    else
        print_status ".env file exists"
    fi
    
    # Check if config file exists
    if [ ! -f user_data/config.telegram.json ]; then
        print_error "user_data/config.telegram.json not found"
        ((errors++))
    else
        print_status "config.telegram.json exists"
    fi
    
    # Check if strategy exists
    if [ ! -f user_data/strategies/HyperliquidStrategy.py ]; then
        print_error "HyperliquidStrategy.py not found"
        ((errors++))
    else
        print_status "HyperliquidStrategy.py exists"
    fi
    
    # Check if docker-compose file exists
    if [ ! -f docker-compose.telegram.yml ]; then
        print_error "docker-compose.telegram.yml not found"
        ((errors++))
    else
        print_status "docker-compose.telegram.yml exists"
    fi
    
    if [ $errors -gt 0 ]; then
        echo ""
        print_error "Found $errors file errors. Please fix them before deploying."
        return 1
    fi
    
    return 0
}

# Test environment variables
test_env_vars() {
    local errors=0
    
    print_header "🔧 Testing Environment Variables"
    echo ""
    
    if [ ! -f .env ]; then
        print_error "Cannot test environment variables - .env file missing"
        return 1
    fi
    
    # Load environment variables safely
    set -a
    source .env 2>/dev/null || {
        print_error "Failed to load .env file - check syntax"
        return 1
    }
    set +a
    
    # Test Hyperliquid credentials
    if [[ -z "$HYPERLIQUID_WALLET_ADDRESS" ]]; then
        print_error "HYPERLIQUID_WALLET_ADDRESS not set"
        ((errors++))
    elif ! validate_wallet_address "$HYPERLIQUID_WALLET_ADDRESS"; then
        print_error "HYPERLIQUID_WALLET_ADDRESS invalid format"
        echo "  Expected: 0x followed by 40 hex characters"
        ((errors++))
    else
        print_status "HYPERLIQUID_WALLET_ADDRESS valid"
    fi
    
    if [[ -z "$HYPERLIQUID_PRIVATE_KEY" ]]; then
        print_error "HYPERLIQUID_PRIVATE_KEY not set"
        ((errors++))
    elif ! validate_private_key "$HYPERLIQUID_PRIVATE_KEY"; then
        print_error "HYPERLIQUID_PRIVATE_KEY invalid format"
        echo "  Expected: 0x followed by 64 hex characters"
        ((errors++))
    else
        print_status "HYPERLIQUID_PRIVATE_KEY valid"
    fi
    
    # Test Telegram credentials
    if [[ -z "$TELEGRAM_BOT_TOKEN" ]]; then
        print_error "TELEGRAM_BOT_TOKEN not set"
        ((errors++))
    elif [[ ! "$TELEGRAM_BOT_TOKEN" =~ ^[0-9]+:[A-Za-z0-9_-]+$ ]]; then
        print_error "TELEGRAM_BOT_TOKEN invalid format"
        echo "  Expected: numbers:letters format"
        ((errors++))
    else
        print_status "TELEGRAM_BOT_TOKEN valid"
    fi
    
    if [[ -z "$TELEGRAM_CHAT_ID" ]]; then
        print_error "TELEGRAM_CHAT_ID not set"
        ((errors++))
    elif [[ ! "$TELEGRAM_CHAT_ID" =~ ^-?[0-9]+$ ]]; then
        print_error "TELEGRAM_CHAT_ID invalid format"
        echo "  Expected: numeric value (can be negative)"
        ((errors++))
    else
        print_status "TELEGRAM_CHAT_ID valid"
    fi
    
    # Test API credentials
    if [[ -z "$API_USERNAME" ]]; then
        print_warning "API_USERNAME not set (will use default)"
    else
        print_status "API_USERNAME set"
    fi
    
    if [[ -z "$API_PASSWORD" ]]; then
        print_warning "API_PASSWORD not set (will use default)"
    else
        print_status "API_PASSWORD set"
    fi
    
    if [ $errors -gt 0 ]; then
        echo ""
        print_error "Found $errors environment variable errors"
        return 1
    fi
    
    return 0
}

# Test Docker files
test_docker_config() {
    print_header "🐳 Testing Docker Configuration"
    echo ""
    
    # Check docker-compose syntax
    if command -v docker-compose &> /dev/null; then
        if docker-compose -f docker-compose.telegram.yml config &> /dev/null; then
            print_status "docker-compose.telegram.yml syntax valid"
        else
            print_error "docker-compose.telegram.yml syntax invalid"
            return 1
        fi
    else
        print_warning "docker-compose not available - skipping syntax check"
    fi
    
    return 0
}

# Show configuration summary
show_summary() {
    print_header "📊 Configuration Summary"
    echo ""
    
    if [ -f .env ]; then
        set -a
        source .env 2>/dev/null
        set +a
        
        echo "Hyperliquid Configuration:"
        echo "  Wallet: ${HYPERLIQUID_WALLET_ADDRESS:0:10}...${HYPERLIQUID_WALLET_ADDRESS: -6}"
        echo "  Private Key: ${HYPERLIQUID_PRIVATE_KEY:0:10}...***"
        echo ""
        echo "Telegram Configuration:"
        echo "  Bot Token: ${TELEGRAM_BOT_TOKEN:0:10}...***"
        echo "  Chat ID: $TELEGRAM_CHAT_ID"
        echo ""
        echo "Trading Configuration:"
        echo "  Strategy: ${STRATEGY:-HyperliquidStrategy}"
        echo "  Timezone: ${TZ:-UTC}"
    else
        print_error "No .env file found"
    fi
}

# Main test function
main() {
    local total_errors=0
    
    # Run all tests
    test_config || ((total_errors++))
    echo ""
    
    test_env_vars || ((total_errors++))
    echo ""
    
    test_docker_config || ((total_errors++))
    echo ""
    
    show_summary
    echo ""
    
    # Final result
    if [ $total_errors -eq 0 ]; then
        print_header "🎉 All Tests Passed!"
        echo ""
        print_status "Your configuration is ready for deployment"
        echo ""
        echo "Next steps:"
        echo "1. Start Docker Desktop"
        echo "2. Run: ./deploy-hyperliquid.sh deploy"
        echo "3. Test your Telegram bot"
    else
        print_header "❌ Tests Failed"
        echo ""
        print_error "Found issues in your configuration"
        echo ""
        echo "Fix the errors above and run this test again:"
        echo "  ./test-config.sh"
    fi
}

# Run the tests
main
