#!/bin/bash
# Freqtrade Development Helper Script

case "$1" in
    "start")
        echo "Starting development environment..."
        docker-compose -f docker-compose.dev.yml up -d freqtrade-dev
        echo "Development container started. Use 'dev.sh shell' to access it."
        ;;
    "stop")
        echo "Stopping development environment..."
        docker-compose -f docker-compose.dev.yml down
        ;;
    "shell")
        echo "Accessing development shell..."
        docker exec -it freqtrade-dev bash
        ;;
    "logs")
        docker-compose -f docker-compose.dev.yml logs -f freqtrade-dev
        ;;
    "build")
        echo "Rebuilding development image..."
        docker-compose -f docker-compose.dev.yml build freqtrade-dev
        ;;
    "test")
        echo "Running tests..."
        docker exec -it freqtrade-dev pytest tests/
        ;;
    "backtest")
        shift
        echo "Running backtest with args: $@"
        docker-compose -f docker-compose.dev.yml run --rm freqtrade-backtest freqtrade backtesting $@
        ;;
    "strategy")
        if [ -z "$2" ]; then
            echo "Usage: dev.sh strategy <strategy_name>"
            exit 1
        fi
        echo "Creating new strategy: $2"
        docker exec -it freqtrade-dev freqtrade new-strategy --strategy $2 --userdir user_data
        ;;
    "download")
        shift
        echo "Downloading data with args: $@"
        docker exec -it freqtrade-dev freqtrade download-data $@
        ;;
    *)
        echo "Freqtrade Development Helper"
        echo "Usage: $0 {start|stop|shell|logs|build|test|backtest|strategy|download}"
        echo ""
        echo "Commands:"
        echo "  start     - Start development container"
        echo "  stop      - Stop development container"
        echo "  shell     - Access development shell"
        echo "  logs      - Show container logs"
        echo "  build     - Rebuild development image"
        echo "  test      - Run tests"
        echo "  backtest  - Run backtesting"
        echo "  strategy  - Create new strategy"
        echo "  download  - Download market data"
        ;;
esac
