---
services:
  freqtrade-dev:
    build:
      context: .
      dockerfile: "./Dockerfile.dev"
    container_name: freqtrade-dev
    volumes:
      - "./user_data:/freqtrade/user_data"
      - "./freqtrade:/freqtrade/freqtrade"
      - "./tests:/freqtrade/tests"
      - "./scripts:/freqtrade/scripts"
    ports:
      - "127.0.0.1:8080:8080"
    environment:
      - PYTHONPATH=/freqtrade
    # Keep container running for development
    entrypoint: ["bash"]
    command: ["-c", "sleep infinity"]
    
  freqtrade-backtest:
    build:
      context: .
      dockerfile: "./Dockerfile.dev"
    container_name: freqtrade-backtest
    volumes:
      - "./user_data:/freqtrade/user_data"
      - "./freqtrade:/freqtrade/freqtrade"
    environment:
      - PYTHONPATH=/freqtrade
    profiles:
      - backtest
    
  freqtrade-jupyter:
    build:
      context: .
      dockerfile: "./docker/Dockerfile.jupyter"
    container_name: freqtrade-jupyter
    volumes:
      - "./user_data:/freqtrade/user_data"
      - "./freqtrade:/freqtrade/freqtrade"
    ports:
      - "127.0.0.1:8888:8888"
    profiles:
      - jupyter
