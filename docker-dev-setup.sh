#!/bin/bash

# Freqtrade Docker Development Setup Script
# This script helps you set up a complete Docker development environment for Freqtrade

set -e

echo "🚀 Freqtrade Docker Development Setup"
echo "======================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}$1${NC}"
}

# Check if Docker is installed
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_status "Docker and Docker Compose are installed ✓"
}

# Create user_data directory structure
setup_user_data() {
    print_header "Setting up user_data directory structure..."
    
    mkdir -p user_data/{strategies,data,logs,notebooks,hyperopts,freqaimodels,backtest_results}
    
    # Create a sample config if it doesn't exist
    if [ ! -f user_data/config.json ]; then
        print_status "Creating sample configuration..."
        cat > user_data/config.json << 'EOF'
{
    "max_open_trades": 3,
    "stake_currency": "USDT",
    "stake_amount": 100,
    "tradable_balance_ratio": 0.99,
    "fiat_display_currency": "USD",
    "dry_run": true,
    "dry_run_wallet": 1000,
    "cancel_open_orders_on_exit": false,
    "trading_mode": "spot",
    "margin_mode": "",
    "unfilledtimeout": {
        "entry": 10,
        "exit": 10,
        "exit_timeout_count": 0,
        "unit": "minutes"
    },
    "entry_pricing": {
        "price_side": "same",
        "use_order_book": true,
        "order_book_top": 1,
        "price_last_balance": 0.0,
        "check_depth_of_market": {
            "enabled": false,
            "bids_to_ask_delta": 1
        }
    },
    "exit_pricing": {
        "price_side": "same",
        "use_order_book": true,
        "order_book_top": 1
    },
    "exchange": {
        "name": "binance",
        "key": "",
        "secret": "",
        "ccxt_config": {},
        "ccxt_async_config": {},
        "pair_whitelist": [
            "BTC/USDT",
            "ETH/USDT",
            "ADA/USDT"
        ],
        "pair_blacklist": []
    },
    "pairlists": [
        {"method": "StaticPairList"}
    ],
    "edge": {
        "enabled": false,
        "process_throttle_secs": 3600,
        "calculate_since_number_of_days": 7,
        "allowed_risk": 0.01,
        "stoploss_range_min": -0.01,
        "stoploss_range_max": -0.1,
        "stoploss_range_step": -0.01,
        "minimum_winrate": 0.60,
        "minimum_expectancy": 0.20,
        "min_trade_number": 10,
        "max_trade_duration_minute": 1440,
        "remove_pumps": false
    },
    "telegram": {
        "enabled": false,
        "token": "",
        "chat_id": ""
    },
    "api_server": {
        "enabled": true,
        "listen_ip_address": "0.0.0.0",
        "listen_port": 8080,
        "verbosity": "error",
        "enable_openapi": false,
        "jwt_secret_key": "somethingrandom",
        "CORS_origins": [],
        "username": "freqtrader",
        "password": "SuperSecretPassword"
    },
    "bot_name": "freqtrade",
    "initial_state": "running",
    "force_entry_enable": false,
    "internals": {
        "process_throttle_secs": 5
    }
}
EOF
        print_status "Sample config created at user_data/config.json"
        print_warning "Remember to update the exchange credentials and other settings!"
    fi
    
    print_status "User data directory structure created ✓"
}

# Build Docker images
build_images() {
    print_header "Building Docker images..."
    
    print_status "Building production image..."
    docker build -t freqtrade-local:latest .
    
    print_status "Building development image..."
    docker build -f Dockerfile.dev -t freqtrade-local:dev .
    
    print_status "Docker images built successfully ✓"
}

# Create development docker-compose file
create_dev_compose() {
    print_header "Creating development docker-compose file..."
    
    cat > docker-compose.dev.yml << 'EOF'
---
services:
  freqtrade-dev:
    build:
      context: .
      dockerfile: "./Dockerfile.dev"
    container_name: freqtrade-dev
    volumes:
      - "./user_data:/freqtrade/user_data"
      - "./freqtrade:/freqtrade/freqtrade"
      - "./tests:/freqtrade/tests"
      - "./scripts:/freqtrade/scripts"
    ports:
      - "127.0.0.1:8080:8080"
    environment:
      - PYTHONPATH=/freqtrade
    # Keep container running for development
    entrypoint: ["bash"]
    command: ["-c", "sleep infinity"]
    
  freqtrade-backtest:
    build:
      context: .
      dockerfile: "./Dockerfile.dev"
    container_name: freqtrade-backtest
    volumes:
      - "./user_data:/freqtrade/user_data"
      - "./freqtrade:/freqtrade/freqtrade"
    environment:
      - PYTHONPATH=/freqtrade
    profiles:
      - backtest
    
  freqtrade-jupyter:
    build:
      context: .
      dockerfile: "./docker/Dockerfile.jupyter"
    container_name: freqtrade-jupyter
    volumes:
      - "./user_data:/freqtrade/user_data"
      - "./freqtrade:/freqtrade/freqtrade"
    ports:
      - "127.0.0.1:8888:8888"
    profiles:
      - jupyter
EOF
    
    print_status "Development docker-compose file created ✓"
}

# Create helper scripts
create_helper_scripts() {
    print_header "Creating helper scripts..."
    
    # Create development script
    cat > dev.sh << 'EOF'
#!/bin/bash
# Freqtrade Development Helper Script

case "$1" in
    "start")
        echo "Starting development environment..."
        docker-compose -f docker-compose.dev.yml up -d freqtrade-dev
        echo "Development container started. Use 'dev.sh shell' to access it."
        ;;
    "stop")
        echo "Stopping development environment..."
        docker-compose -f docker-compose.dev.yml down
        ;;
    "shell")
        echo "Accessing development shell..."
        docker exec -it freqtrade-dev bash
        ;;
    "logs")
        docker-compose -f docker-compose.dev.yml logs -f freqtrade-dev
        ;;
    "build")
        echo "Rebuilding development image..."
        docker-compose -f docker-compose.dev.yml build freqtrade-dev
        ;;
    "test")
        echo "Running tests..."
        docker exec -it freqtrade-dev pytest tests/
        ;;
    "backtest")
        shift
        echo "Running backtest with args: $@"
        docker-compose -f docker-compose.dev.yml run --rm freqtrade-backtest freqtrade backtesting $@
        ;;
    "strategy")
        if [ -z "$2" ]; then
            echo "Usage: dev.sh strategy <strategy_name>"
            exit 1
        fi
        echo "Creating new strategy: $2"
        docker exec -it freqtrade-dev freqtrade new-strategy --strategy $2 --userdir user_data
        ;;
    "download")
        shift
        echo "Downloading data with args: $@"
        docker exec -it freqtrade-dev freqtrade download-data $@
        ;;
    *)
        echo "Freqtrade Development Helper"
        echo "Usage: $0 {start|stop|shell|logs|build|test|backtest|strategy|download}"
        echo ""
        echo "Commands:"
        echo "  start     - Start development container"
        echo "  stop      - Stop development container"
        echo "  shell     - Access development shell"
        echo "  logs      - Show container logs"
        echo "  build     - Rebuild development image"
        echo "  test      - Run tests"
        echo "  backtest  - Run backtesting"
        echo "  strategy  - Create new strategy"
        echo "  download  - Download market data"
        ;;
esac
EOF
    
    chmod +x dev.sh
    print_status "Helper script 'dev.sh' created ✓"
}

# Main setup function
main() {
    print_header "🐳 Starting Freqtrade Docker Development Setup"
    
    check_docker
    setup_user_data
    build_images
    create_dev_compose
    create_helper_scripts
    
    print_header "🎉 Setup Complete!"
    echo ""
    print_status "Your Freqtrade Docker development environment is ready!"
    echo ""
    echo "Next steps:"
    echo "1. Update user_data/config.json with your exchange credentials"
    echo "2. Start development environment: ./dev.sh start"
    echo "3. Access development shell: ./dev.sh shell"
    echo "4. Create your first strategy: ./dev.sh strategy MyStrategy"
    echo ""
    echo "Available commands:"
    echo "  ./dev.sh start     - Start development container"
    echo "  ./dev.sh shell     - Access development shell"
    echo "  ./dev.sh test      - Run tests"
    echo "  ./dev.sh backtest  - Run backtesting"
    echo ""
    print_warning "Remember to set dry_run: false in config.json when ready for live trading!"
}

# Run main function
main "$@"
