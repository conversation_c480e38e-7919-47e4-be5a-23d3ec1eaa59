---
services:
  freqtrade-telegram:
    build:
      context: .
      dockerfile: "./Dockerfile"
    container_name: freqtrade-telegram-bot
    restart: unless-stopped
    volumes:
      - "./user_data:/freqtrade/user_data"
      # Mount custom strategies
      - "./user_data/strategies:/freqtrade/user_data/strategies"
    ports:
      - "127.0.0.1:8080:8080"
    environment:
      # Hyperliquid credentials (DEX requires wallet address and private key)
      - HYPERLIQUID_WALLET_ADDRESS=${HYPERLIQUID_WALLET_ADDRESS}
      - HYPERLIQUID_PRIVATE_KEY=${HYPERLIQUID_PRIVATE_KEY}

      # Telegram Bot credentials
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN}
      - TELEGRAM_CHAT_ID=${TELEGRAM_CHAT_ID}

      # API Server credentials
      - API_USERNAME=${API_USERNAME:-freqtrader}
      - API_PASSWORD=${API_PASSWORD:-SuperSecretPassword}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-your-secret-jwt-key-change-this}

      # Optional: Set timezone
      - TZ=${TZ:-UTC}
    command: >
      trade
      --logfile /freqtrade/user_data/logs/freqtrade.log
      --db-url sqlite:////freqtrade/user_data/tradesv3.sqlite
      --config /freqtrade/user_data/config.telegram.json
      --strategy ${STRATEGY:-CustomStrategy}
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/v1/ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Optional: Separate service for backtesting
  freqtrade-backtest:
    build:
      context: .
      dockerfile: "./Dockerfile"
    container_name: freqtrade-backtest
    volumes:
      - "./user_data:/freqtrade/user_data"
    environment:
      - HYPERLIQUID_WALLET_ADDRESS=${HYPERLIQUID_WALLET_ADDRESS}
      - HYPERLIQUID_PRIVATE_KEY=${HYPERLIQUID_PRIVATE_KEY}
    profiles:
      - backtest
    command: >
      backtesting
      --config /freqtrade/user_data/config.telegram.json
      --strategy ${STRATEGY:-CustomStrategy}
      --timerange ${TIMERANGE:-20231101-20231201}

  # Optional: Data downloader service
  freqtrade-download:
    build:
      context: .
      dockerfile: "./Dockerfile"
    container_name: freqtrade-download
    volumes:
      - "./user_data:/freqtrade/user_data"
    environment:
      - HYPERLIQUID_WALLET_ADDRESS=${HYPERLIQUID_WALLET_ADDRESS}
      - HYPERLIQUID_PRIVATE_KEY=${HYPERLIQUID_PRIVATE_KEY}
    profiles:
      - download
    command: >
      download-data
      --config /freqtrade/user_data/config.telegram.json
      --exchange ${EXCHANGE:-hyperliquid}
      --pairs ${PAIRS:-BTC/USDC:USDC ETH/USDC:USDC}
      --timeframes ${TIMEFRAMES:-1h 4h}
      --days ${DAYS:-30}
