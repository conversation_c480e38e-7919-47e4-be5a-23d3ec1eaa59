# 🚀 Freqtrade Hyperliquid Telegram Bot Deployment Guide

This guide will help you deploy Freqtrade with Telegram bot functionality specifically configured for Hyperliquid exchange.

## 🎯 What You'll Get

- ✅ **Freqtrade bot** trading on Hyperliquid DEX
- ✅ **Telegram bot** for remote control and notifications
- ✅ **Web UI** for monitoring and management
- ✅ **Docker deployment** for easy management
- ✅ **Futures trading** with leverage support
- ✅ **Stoploss on exchange** support

## 📋 Prerequisites

### 1. System Requirements
- Docker and Docker Compose installed
- At least 2GB RAM and 1GB disk space
- Stable internet connection

### 2. Hyperliquid Setup
- Ethereum wallet with USDC on Arbitrum One
- Hyperliquid account with deposited USDC
- API wallet created (for security)

### 3. Telegram Setup
- Telegram account
- Bot token from @BotFather

## 🔧 Step-by-Step Deployment

### Step 1: Create Hyperliquid API Wallet

1. **Visit Hyperliquid API Generator**
   - Go to: https://app.hyperliquid.xyz/API
   - Connect your main wallet

2. **Generate API Wallet**
   - Click "Generate API Wallet"
   - **IMPORTANT**: Copy and save both:
     - Your main wallet address (0x...)
     - The API wallet private key (0x...)

3. **Security Notes**
   - ✅ API wallet can only trade (no withdrawals)
   - ✅ Keep private key secure and encrypted
   - ❌ Never use your main wallet private key

### Step 2: Create Telegram Bot

1. **Message @BotFather on Telegram**
   ```
   /newbot
   ```

2. **Follow the prompts**
   - Choose a name for your bot
   - Choose a username (must end with 'bot')
   - Copy the bot token

3. **Get Your Chat ID**
   - Message your new bot
   - Visit: `https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates`
   - Find your chat ID in the response

### Step 3: Configure Environment

1. **Copy the environment template**
   ```bash
   cp .env.hyperliquid.template .env
   ```

2. **Edit the .env file**
   ```bash
   # Use your preferred editor
   nano .env
   # or
   code .env
   ```

3. **Fill in your credentials**
   ```bash
   # Hyperliquid credentials
   HYPERLIQUID_WALLET_ADDRESS=0xYourMainWalletAddress
   HYPERLIQUID_PRIVATE_KEY=0xYourAPIWalletPrivateKey
   
   # Telegram credentials
   TELEGRAM_BOT_TOKEN=1234567890:YourBotToken
   TELEGRAM_CHAT_ID=YourChatID
   
   # API credentials (change these!)
   API_USERNAME=freqtrader
   API_PASSWORD=YourSecurePassword
   JWT_SECRET_KEY=YourSecretJWTKey
   ```

### Step 4: Deploy the Bot

1. **Run the deployment script**
   ```bash
   ./deploy-hyperliquid.sh deploy
   ```

2. **The script will:**
   - ✅ Validate your configuration
   - ✅ Build the Docker image
   - ✅ Start the Telegram bot
   - ✅ Show deployment status

### Step 5: Test Your Bot

1. **Send Telegram commands**
   ```
   /start    - Start the bot
   /status   - Check trading status
   /balance  - Check account balance
   /help     - Show all commands
   ```

2. **Access Web UI**
   - Open: http://localhost:8080
   - Login with your API credentials

## 🎮 Bot Management Commands

### Deployment Script Commands
```bash
./deploy-hyperliquid.sh deploy   # Full deployment
./deploy-hyperliquid.sh stop     # Stop the bot
./deploy-hyperliquid.sh logs     # View logs
./deploy-hyperliquid.sh restart  # Restart bot
./deploy-hyperliquid.sh status   # Check status
```

### Docker Commands
```bash
# View logs
docker-compose -f docker-compose.telegram.yml logs -f

# Stop bot
docker-compose -f docker-compose.telegram.yml down

# Restart bot
docker-compose -f docker-compose.telegram.yml restart

# Check status
docker-compose -f docker-compose.telegram.yml ps
```

### Telegram Bot Commands
```
/start        - Start the trader
/stop         - Stop the trader
/status       - Show open trades
/balance      - Show account balance
/profit       - Show profit/loss
/performance  - Show performance stats
/daily        - Show daily stats
/help         - Show all commands
```

## 📊 Monitoring Your Bot

### 1. Telegram Notifications
Your bot will send notifications for:
- ✅ Trade entries and exits
- ✅ Profit/loss updates
- ✅ System status changes
- ✅ Errors and warnings

### 2. Web Interface
Access at http://localhost:8080:
- 📈 Real-time trading dashboard
- 📊 Performance charts
- 🔧 Configuration management
- 📋 Trade history

### 3. Log Files
```bash
# View live logs
./deploy-hyperliquid.sh logs

# Check log files
ls user_data/logs/
```

## ⚙️ Configuration

### Trading Configuration
Edit `user_data/config.telegram.json`:

```json
{
  "dry_run": true,              // Start with dry run!
  "max_open_trades": 3,         // Max concurrent trades
  "stake_amount": 100,          // USDC per trade
  "trading_mode": "futures",    // Hyperliquid is futures
  "margin_mode": "isolated",    // Recommended for safety
}
```

### Hyperliquid Specific Settings
```json
{
  "exchange": {
    "name": "hyperliquid",
    "walletAddress": "0x...",    // Your main wallet
    "privateKey": "0x...",       // API wallet private key
    "pair_whitelist": [
      "BTC/USDC:USDC",
      "ETH/USDC:USDC",
      "SOL/USDC:USDC"
    ]
  }
}
```

## 🚨 Important Security Notes

### 🔒 Security Best Practices
1. **Use API Wallet Only**
   - ✅ Never use main wallet private key
   - ✅ API wallet can't withdraw funds
   - ✅ Limits exposure to trading only

2. **Environment Security**
   - ✅ Never commit .env to version control
   - ✅ Use strong passwords
   - ✅ Keep private keys encrypted

3. **Trading Safety**
   - ✅ Start with dry_run: true
   - ✅ Test thoroughly before going live
   - ✅ Use small amounts initially
   - ✅ Monitor trades closely

### 🛡️ Hyperliquid Specific Security
1. **Wallet Security**
   - Use hardware wallet for main wallet
   - Keep API wallet separate
   - Regular security audits

2. **DEX Considerations**
   - Understand smart contract risks
   - Monitor gas fees on Arbitrum
   - Keep some ETH for gas

## 🔧 Troubleshooting

### Common Issues

1. **Bot won't start**
   ```bash
   # Check logs
   ./deploy-hyperliquid.sh logs
   
   # Verify configuration
   ./deploy-hyperliquid.sh status
   ```

2. **Telegram not working**
   - Verify bot token and chat ID
   - Check if bot is blocked
   - Test with /start command

3. **Exchange connection issues**
   - Verify wallet address format
   - Check private key format
   - Ensure USDC balance on Hyperliquid

4. **Docker issues**
   ```bash
   # Rebuild image
   docker-compose -f docker-compose.telegram.yml build --no-cache
   
   # Reset everything
   docker-compose -f docker-compose.telegram.yml down
   docker system prune -f
   ```

### Getting Help
- 📚 Freqtrade Docs: https://www.freqtrade.io/
- 💬 Discord: https://discord.gg/p7nuUNVfP7
- 🐛 GitHub Issues: https://github.com/freqtrade/freqtrade/issues
- 📖 Hyperliquid Docs: https://hyperliquid.gitbook.io/

## 🎯 Going Live

### Before Enabling Live Trading

1. **Thorough Testing**
   - Run in dry-run mode for several days
   - Test all Telegram commands
   - Verify strategy performance

2. **Risk Management**
   - Set appropriate stake amounts
   - Configure stop losses
   - Limit max open trades

3. **Enable Live Trading**
   ```bash
   # Edit config
   nano user_data/config.telegram.json
   
   # Change dry_run to false
   "dry_run": false,
   
   # Restart bot
   ./deploy-hyperliquid.sh restart
   ```

## 🎉 You're Ready!

Your Freqtrade Telegram bot is now deployed and ready to trade on Hyperliquid! 

Remember:
- 🧪 Start with dry-run mode
- 📱 Test Telegram commands
- 📊 Monitor via web UI
- 🔒 Keep security in mind
- 💰 Start small when going live

Happy trading! 🚀
