#!/bin/bash

# Hyperliquid Environment Setup Script
# This script helps you set up the environment for Hyperliquid trading

set -e

echo "🔧 Hyperliquid Environment Setup"
echo "================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}$1${NC}"
}

print_important() {
    echo -e "${PURPLE}[IMPORTANT]${NC} $1"
}

# Function to validate wallet address
validate_wallet_address() {
    local address=$1
    if [[ ! "$address" =~ ^0x[a-fA-F0-9]{40}$ ]]; then
        return 1
    fi
    return 0
}

# Function to validate private key
validate_private_key() {
    local key=$1
    if [[ ! "$key" =~ ^0x[a-fA-F0-9]{64}$ ]]; then
        return 1
    fi
    return 0
}

# Interactive setup
interactive_setup() {
    print_header "🚀 Interactive Hyperliquid Setup"
    echo ""
    
    # Check if .env already exists
    if [ -f .env ]; then
        print_warning ".env file already exists!"
        read -p "Do you want to overwrite it? (y/N): " overwrite
        if [[ ! $overwrite =~ ^[Yy]$ ]]; then
            print_status "Keeping existing .env file"
            exit 0
        fi
    fi
    
    # Copy template
    if [ ! -f .env.hyperliquid.template ]; then
        print_error ".env.hyperliquid.template not found!"
        exit 1
    fi
    
    cp .env.hyperliquid.template .env
    print_status "Created .env file from template"
    
    echo ""
    print_header "📝 Let's configure your environment step by step..."
    echo ""
    
    # Hyperliquid Wallet Address
    print_important "Step 1: Hyperliquid Wallet Configuration"
    echo "You need your main wallet address and API wallet private key."
    echo "Get these from: https://app.hyperliquid.xyz/API"
    echo ""
    
    while true; do
        read -p "Enter your main wallet address (0x...): " wallet_address
        if validate_wallet_address "$wallet_address"; then
            break
        else
            print_error "Invalid wallet address format. Should be 0x followed by 40 hex characters"
        fi
    done
    
    while true; do
        read -s -p "Enter your API wallet private key (0x...): " private_key
        echo ""
        if validate_private_key "$private_key"; then
            break
        else
            print_error "Invalid private key format. Should be 0x followed by 64 hex characters"
        fi
    done
    
    # Telegram Bot Configuration
    echo ""
    print_important "Step 2: Telegram Bot Configuration"
    echo "Create a bot with @BotFather on Telegram first."
    echo ""
    
    read -p "Enter your Telegram bot token: " telegram_token
    read -p "Enter your Telegram chat ID: " telegram_chat_id
    
    # API Server Configuration
    echo ""
    print_important "Step 3: API Server Configuration"
    echo "These credentials will be used for the web interface."
    echo ""
    
    read -p "Enter API username (default: freqtrader): " api_username
    api_username=${api_username:-freqtrader}
    
    read -s -p "Enter API password: " api_password
    echo ""
    
    # Generate JWT secret
    jwt_secret=$(openssl rand -hex 32 2>/dev/null || echo "$(date +%s)-$(whoami)-$(hostname)" | sha256sum | cut -d' ' -f1)
    
    # Trading Configuration
    echo ""
    print_important "Step 4: Trading Configuration"
    echo ""
    
    read -p "Enter strategy name (default: CustomStrategy): " strategy
    strategy=${strategy:-CustomStrategy}
    
    read -p "Enter timezone (default: UTC): " timezone
    timezone=${timezone:-UTC}
    
    # Update .env file
    print_header "💾 Updating .env file..."
    
    # Use sed to replace values in .env file
    sed -i.bak "s|HYPERLIQUID_WALLET_ADDRESS=.*|HYPERLIQUID_WALLET_ADDRESS=$wallet_address|" .env
    sed -i.bak "s|HYPERLIQUID_PRIVATE_KEY=.*|HYPERLIQUID_PRIVATE_KEY=$private_key|" .env
    sed -i.bak "s|TELEGRAM_BOT_TOKEN=.*|TELEGRAM_BOT_TOKEN=$telegram_token|" .env
    sed -i.bak "s|TELEGRAM_CHAT_ID=.*|TELEGRAM_CHAT_ID=$telegram_chat_id|" .env
    sed -i.bak "s|API_USERNAME=.*|API_USERNAME=$api_username|" .env
    sed -i.bak "s|API_PASSWORD=.*|API_PASSWORD=$api_password|" .env
    sed -i.bak "s|JWT_SECRET_KEY=.*|JWT_SECRET_KEY=$jwt_secret|" .env
    sed -i.bak "s|STRATEGY=.*|STRATEGY=$strategy|" .env
    sed -i.bak "s|TZ=.*|TZ=$timezone|" .env
    
    # Remove backup file
    rm .env.bak 2>/dev/null || true
    
    print_status "Environment file configured successfully!"
    
    echo ""
    print_header "✅ Setup Complete!"
    echo ""
    print_status "Your .env file has been configured with:"
    echo "  • Hyperliquid wallet: ${wallet_address:0:10}...${wallet_address: -6}"
    echo "  • Telegram bot configured"
    echo "  • API server configured"
    echo "  • Strategy: $strategy"
    echo ""
    print_important "Next steps:"
    echo "1. Review your configuration: cat .env"
    echo "2. Deploy the bot: ./deploy-hyperliquid.sh deploy"
    echo "3. Test with Telegram commands"
    echo ""
    print_warning "Security reminders:"
    echo "• Never share your private key"
    echo "• Start with dry_run=true"
    echo "• Monitor your trades closely"
}

# Show current configuration
show_config() {
    if [ ! -f .env ]; then
        print_error ".env file not found. Run setup first."
        exit 1
    fi
    
    print_header "📋 Current Configuration"
    echo ""
    
    source .env
    
    echo "Hyperliquid Configuration:"
    echo "  Wallet Address: ${HYPERLIQUID_WALLET_ADDRESS:0:10}...${HYPERLIQUID_WALLET_ADDRESS: -6}"
    echo "  Private Key: ${HYPERLIQUID_PRIVATE_KEY:0:10}...***"
    echo ""
    echo "Telegram Configuration:"
    echo "  Bot Token: ${TELEGRAM_BOT_TOKEN:0:10}...***"
    echo "  Chat ID: $TELEGRAM_CHAT_ID"
    echo ""
    echo "API Configuration:"
    echo "  Username: $API_USERNAME"
    echo "  Password: ***"
    echo ""
    echo "Trading Configuration:"
    echo "  Strategy: $STRATEGY"
    echo "  Timezone: $TZ"
}

# Validate current configuration
validate_config() {
    if [ ! -f .env ]; then
        print_error ".env file not found. Run setup first."
        exit 1
    fi
    
    print_header "🔍 Validating Configuration"
    
    source .env
    
    # Check required variables
    local errors=0
    
    if [[ -z "$HYPERLIQUID_WALLET_ADDRESS" ]]; then
        print_error "HYPERLIQUID_WALLET_ADDRESS not set"
        ((errors++))
    elif ! validate_wallet_address "$HYPERLIQUID_WALLET_ADDRESS"; then
        print_error "Invalid HYPERLIQUID_WALLET_ADDRESS format"
        ((errors++))
    fi
    
    if [[ -z "$HYPERLIQUID_PRIVATE_KEY" ]]; then
        print_error "HYPERLIQUID_PRIVATE_KEY not set"
        ((errors++))
    elif ! validate_private_key "$HYPERLIQUID_PRIVATE_KEY"; then
        print_error "Invalid HYPERLIQUID_PRIVATE_KEY format"
        ((errors++))
    fi
    
    if [[ -z "$TELEGRAM_BOT_TOKEN" ]]; then
        print_error "TELEGRAM_BOT_TOKEN not set"
        ((errors++))
    fi
    
    if [[ -z "$TELEGRAM_CHAT_ID" ]]; then
        print_error "TELEGRAM_CHAT_ID not set"
        ((errors++))
    fi
    
    if [ $errors -eq 0 ]; then
        print_status "✅ Configuration is valid!"
    else
        print_error "❌ Found $errors configuration errors"
        exit 1
    fi
}

# Main function
case "$1" in
    "setup")
        interactive_setup
        ;;
    "show")
        show_config
        ;;
    "validate")
        validate_config
        ;;
    *)
        echo "Hyperliquid Environment Setup"
        echo "Usage: $0 {setup|show|validate}"
        echo ""
        echo "Commands:"
        echo "  setup     - Interactive environment setup"
        echo "  show      - Show current configuration"
        echo "  validate  - Validate configuration"
        echo ""
        echo "Example:"
        echo "  $0 setup    # Run interactive setup"
        ;;
esac
