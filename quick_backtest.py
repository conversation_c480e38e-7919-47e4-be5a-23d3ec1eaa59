#!/usr/bin/env python3
"""
Quick Backtest Script for MicroAIAgent Strategy
Tests the strategy to meet our targets:
- Take Profit: 20%
- Stop Loss: 10% 
- Win Rate: >65%
"""

import subprocess
import json
import re

def run_backtest():
    """Run backtest using Docker"""
    print("🧪 Running MicroAIAgent Strategy Backtest...")
    
    # Update config to use MicroAIAgent strategy
    config_path = "user_data/config.backtest.json"
    
    # Read current config
    with open(config_path, 'r') as f:
        config = json.load(f)
    
    # Update strategy
    config["strategy"] = "MicroAIAgent"
    config["max_open_trades"] = 5  # Conservative for better win rate
    config["stake_amount"] = 100   # Higher stake for better profit calculation
    
    # Write updated config
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=4)
    
    print("✅ Updated config to use MicroAIAgent strategy")
    
    # Run backtest via Docker
    cmd = [
        "docker", "exec", "freqtrade-telegram-bot",
        "freqtrade", "backtesting",
        "--config", "/freqtrade/user_data/config.backtest.json",
        "--strategy", "MicroAIAgent",
        "--timerange", "20241201-20250101",
        "--breakdown", "month"
    ]
    
    print("🚀 Executing backtest...")
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode == 0:
        print("✅ Backtest completed successfully!")
        return parse_results(result.stdout)
    else:
        print(f"❌ Backtest failed: {result.stderr}")
        return None

def parse_results(output):
    """Parse backtest results"""
    print("\n📊 BACKTEST RESULTS")
    print("=" * 50)
    
    lines = output.split('\n')
    results = {}
    
    # Find the results table
    for i, line in enumerate(lines):
        if 'TOTAL' in line and '│' in line:
            parts = line.split('│')
            if len(parts) >= 7:
                try:
                    results['total_trades'] = int(parts[1].strip())
                    results['avg_profit_pct'] = float(parts[2].strip())
                    results['total_profit'] = float(parts[3].strip())
                    results['total_profit_pct'] = float(parts[4].strip())
                    
                    # Parse win/loss data
                    win_loss_part = parts[6].strip().split()
                    if len(win_loss_part) >= 4:
                        results['wins'] = int(win_loss_part[0])
                        results['losses'] = int(win_loss_part[2])
                        results['win_rate'] = float(win_loss_part[3])
                    break
                except (ValueError, IndexError):
                    continue
    
    # Print results
    if results:
        total_trades = results.get('total_trades', 0)
        win_rate = results.get('win_rate', 0) / 100 if results.get('win_rate', 0) > 1 else results.get('win_rate', 0)
        avg_profit = results.get('avg_profit_pct', 0) / 100 if results.get('avg_profit_pct', 0) > 1 else results.get('avg_profit_pct', 0)
        total_profit_pct = results.get('total_profit_pct', 0) / 100 if results.get('total_profit_pct', 0) > 1 else results.get('total_profit_pct', 0)
        
        print(f"📈 Total Trades: {total_trades}")
        print(f"🎯 Win Rate: {win_rate:.1%} (Target: >65%)")
        print(f"💰 Avg Profit per Trade: {avg_profit:.1%}")
        print(f"📊 Total Profit: {total_profit_pct:.1%}")
        
        # Analysis
        print("\n🎯 TARGET ANALYSIS:")
        if win_rate >= 0.65:
            print("✅ Win Rate Target Met!")
        else:
            print(f"❌ Win Rate Below Target ({win_rate:.1%} < 65%)")
            
        if total_trades >= 10:
            print("✅ Sufficient Trade Volume")
        else:
            print(f"⚠️ Low Trade Volume ({total_trades} trades)")
            
        # Strategy assessment
        if win_rate >= 0.65 and total_trades >= 10:
            print("\n🎉 STRATEGY APPROVED FOR LIVE TRADING!")
        elif total_trades == 0:
            print("\n🚨 STRATEGY ISSUE: No trades generated")
        else:
            print("\n⚠️ STRATEGY NEEDS OPTIMIZATION")
            
        return results
    else:
        print("❌ Could not parse results")
        print("\nRaw output:")
        print(output)
        return None

def analyze_strategy():
    """Analyze the MicroAIAgent strategy"""
    print("\n🔍 STRATEGY ANALYSIS")
    print("=" * 50)
    
    print("📋 MicroAIAgent Strategy Features:")
    print("   • 20% Take Profit Target")
    print("   • 10% Stop Loss")
    print("   • Multiple Entry Conditions:")
    print("     - RSI Oversold/Overbought")
    print("     - Bollinger Band Bounces")
    print("     - EMA Crossovers")
    print("     - MACD Signals")
    print("     - Volume Confirmation")
    print("   • Dynamic Leverage per Pair")
    print("   • Trailing Stop Loss")
    
    print("\n🎯 Optimization Targets:")
    print("   • Win Rate: >65%")
    print("   • Risk/Reward: 2:1 (20% profit / 10% loss)")
    print("   • Trade Frequency: Active but selective")

if __name__ == "__main__":
    print("🤖 MICRO AI AGENT STRATEGY TESTER")
    print("=" * 50)
    
    # Analyze strategy first
    analyze_strategy()
    
    # Run backtest
    results = run_backtest()
    
    if results:
        print("\n✅ Backtest Analysis Complete!")
        print("📁 Check user_data/backtest_results/ for detailed results")
    else:
        print("\n❌ Backtest failed - check strategy configuration")
