# 🔧 Freqtrade Hyperliquid Deployment Troubleshooting

## Quick Diagnosis

Run this command first to check your setup:
```bash
./test-config.sh
```

## Common Issues and Solutions

### 1. Docker Daemon Not Running

**Error:**
```
Docker daemon is not running!
```

**Solution:**
1. Open Docker Desktop application
2. Wait for it to start completely (green icon in system tray)
3. Run deployment again: `./deploy-hyperliquid.sh deploy`

**On macOS:**
- Docker Desktop should show "Docker Desktop is running" in the menu bar

**On Windows:**
- Docker Desktop icon should be visible in system tray
- Right-click and ensure it says "Docker Desktop is running"

**On Linux:**
```bash
sudo systemctl start docker
sudo systemctl enable docker
```

### 2. Environment File Issues

**Error:**
```
.env: line X: No such file or directory
```

**Solution:**
1. Check .env file syntax:
   ```bash
   ./test-config.sh
   ```
2. Ensure values with spaces are quoted:
   ```bash
   PAIRS="BTC/USDC:USDC ETH/USDC:USDC"
   TIMEFRAMES="1h 4h"
   ```
3. Recreate .env file:
   ```bash
   ./setup-hyperliquid-env.sh setup
   ```

### 3. Invalid Wallet Address

**Error:**
```
Invalid HYPERLIQUID_WALLET_ADDRESS format
```

**Solution:**
- Wallet address must be exactly 42 characters
- Must start with `0x`
- Must contain only hex characters (0-9, a-f, A-F)
- Example: `******************************************`

### 4. Invalid Private Key

**Error:**
```
Invalid HYPERLIQUID_PRIVATE_KEY format
```

**Solution:**
- Private key must be exactly 66 characters
- Must start with `0x`
- Must contain only hex characters (0-9, a-f, A-F)
- Example: `0x04901f56f4b1a521f47e22b1ccccca8ba382051328bd15e560416723a9a54d70`
- **IMPORTANT**: Use API wallet private key, NOT main wallet!

### 5. Telegram Bot Token Issues

**Error:**
```
TELEGRAM_BOT_TOKEN invalid format
```

**Solution:**
1. Create bot with @BotFather:
   - Message @BotFather on Telegram
   - Send `/newbot`
   - Follow instructions
2. Token format: `*********0:ABCdefGHIjklMNOpqrsTUVwxyz`
3. Copy exact token from @BotFather

### 6. Telegram Chat ID Issues

**Error:**
```
TELEGRAM_CHAT_ID invalid format
```

**Solution:**
1. Message your bot first
2. Get chat ID:
   ```
   https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates
   ```
3. Look for `"chat":{"id": YOUR_CHAT_ID}`
4. Can be positive or negative number
5. Example: `*********` or `-4811985405`

### 7. Docker Compose Syntax Error

**Error:**
```
docker-compose.telegram.yml syntax invalid
```

**Solution:**
1. Check YAML syntax
2. Ensure proper indentation (spaces, not tabs)
3. Recreate file if corrupted:
   ```bash
   git checkout docker-compose.telegram.yml
   ```

### 8. Strategy Not Found

**Error:**
```
Strategy 'HyperliquidStrategy' not found
```

**Solution:**
1. Check strategy exists:
   ```bash
   ls user_data/strategies/
   ```
2. Ensure correct name in .env:
   ```bash
   STRATEGY=HyperliquidStrategy
   ```
3. Recreate strategy if missing:
   ```bash
   # Copy from template or recreate
   ```

### 9. Port Already in Use

**Error:**
```
Port 8080 is already in use
```

**Solution:**
1. Stop existing containers:
   ```bash
   docker-compose -f docker-compose.telegram.yml down
   ```
2. Check what's using port 8080:
   ```bash
   lsof -i :8080
   ```
3. Kill the process or change port in docker-compose.telegram.yml

### 10. Permission Denied

**Error:**
```
Permission denied
```

**Solution:**
1. Make scripts executable:
   ```bash
   chmod +x deploy-hyperliquid.sh
   chmod +x setup-hyperliquid-env.sh
   chmod +x test-config.sh
   ```
2. Check Docker permissions:
   ```bash
   sudo usermod -aG docker $USER
   # Then logout and login again
   ```

### 11. Container Won't Start

**Error:**
```
Container exits immediately
```

**Solution:**
1. Check logs:
   ```bash
   ./deploy-hyperliquid.sh logs
   ```
2. Check configuration:
   ```bash
   ./test-config.sh
   ```
3. Rebuild image:
   ```bash
   docker-compose -f docker-compose.telegram.yml build --no-cache
   ```

### 12. Telegram Bot Not Responding

**Error:**
```
Bot doesn't respond to commands
```

**Solution:**
1. Check bot token and chat ID
2. Ensure bot is started:
   ```bash
   ./deploy-hyperliquid.sh status
   ```
3. Check logs for errors:
   ```bash
   ./deploy-hyperliquid.sh logs
   ```
4. Test bot manually:
   ```
   https://api.telegram.org/bot<TOKEN>/getMe
   ```

### 13. Exchange Connection Failed

**Error:**
```
Failed to connect to Hyperliquid
```

**Solution:**
1. Verify wallet credentials
2. Check internet connection
3. Ensure USDC balance on Hyperliquid
4. Verify API wallet permissions

## Diagnostic Commands

### Check Everything
```bash
./test-config.sh
```

### Check Docker
```bash
docker info
docker-compose --version
```

### Check Configuration
```bash
./setup-hyperliquid-env.sh validate
```

### Check Deployment Status
```bash
./deploy-hyperliquid.sh status
```

### View Logs
```bash
./deploy-hyperliquid.sh logs
```

### Reset Everything
```bash
# Stop all containers
./deploy-hyperliquid.sh stop

# Clean Docker
docker system prune -f

# Rebuild and redeploy
./deploy-hyperliquid.sh deploy
```

## Getting Help

### 1. Check Logs First
```bash
./deploy-hyperliquid.sh logs
```

### 2. Test Configuration
```bash
./test-config.sh
```

### 3. Community Support
- **Freqtrade Discord**: https://discord.gg/p7nuUNVfP7
- **GitHub Issues**: https://github.com/freqtrade/freqtrade/issues
- **Documentation**: https://www.freqtrade.io/

### 4. Hyperliquid Specific
- **Hyperliquid Docs**: https://hyperliquid.gitbook.io/
- **Hyperliquid Discord**: Check their official channels

## Emergency Recovery

### Complete Reset
```bash
# Stop everything
./deploy-hyperliquid.sh stop

# Remove containers and images
docker-compose -f docker-compose.telegram.yml down --rmi all

# Clean Docker system
docker system prune -af

# Recreate environment
./setup-hyperliquid-env.sh setup

# Test configuration
./test-config.sh

# Deploy again
./deploy-hyperliquid.sh deploy
```

### Backup Important Data
```bash
# Backup your configuration
cp .env .env.backup
cp user_data/config.telegram.json user_data/config.telegram.json.backup

# Backup strategies
cp -r user_data/strategies user_data/strategies.backup
```

## Prevention Tips

1. **Always test configuration**: Run `./test-config.sh` before deploying
2. **Start with dry run**: Keep `dry_run: true` until thoroughly tested
3. **Monitor logs**: Regularly check `./deploy-hyperliquid.sh logs`
4. **Backup configs**: Keep backups of working configurations
5. **Update regularly**: Keep Docker and scripts updated
6. **Security first**: Never share private keys or commit .env files

---

**Need more help?** Run `./test-config.sh` and share the output when asking for support.
