# Freqtrade Telegram Bot Environment Configuration for Hyperliquid
# Copy this file to .env and fill in your actual values

# ===========================================
# HYPERLIQUID EXCHANGE CONFIGURATION
# ===========================================
# Hyperliquid is a DEX and requires wallet credentials instead of API keys
# Get these from your Hyperliquid API wallet (NOT your main wallet!)

# Your Ethereum wallet address (in hex format: 0x<40 hex characters>)
# This should be your MAIN wallet address, not the API wallet address
HYPERLIQUID_WALLET_ADDRESS=******************************************

# Your API wallet private key (in hex format: 0x<64 hex characters>)
# Generate this from https://app.hyperliquid.xyz/API
# NEVER use your main wallet private key here!
HYPERLIQUID_PRIVATE_KEY=******************************************123456789012345678901234

# ===========================================
# TELEGRAM BOT CONFIGURATION
# ===========================================
# Get these from @BotFather on Telegram
# 1. Message @BotFather on Telegram
# 2. Send /newbot and follow instructions
# 3. Copy the token here
TELEGRAM_BOT_TOKEN=1234567890:ABCdefGHIjklMNOpqrsTUVwxyz

# Your Telegram Chat ID (can be personal or group chat)
# To get your chat ID:
# 1. Message your bot
# 2. Visit: https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates
# 3. Look for "chat":{"id": YOUR_CHAT_ID}
TELEGRAM_CHAT_ID=123456789

# ===========================================
# API SERVER CONFIGURATION
# ===========================================
# Credentials for FreqUI web interface
API_USERNAME=freqtrader
API_PASSWORD=SuperSecretPassword123!
JWT_SECRET_KEY=your-very-secret-jwt-key-change-this-to-something-random-and-long

# ===========================================
# TRADING CONFIGURATION
# ===========================================
# Strategy to use (must exist in user_data/strategies/)
STRATEGY=CustomStrategy

# Timezone (optional)
TZ=UTC

# ===========================================
# BACKTESTING CONFIGURATION (Optional)
# ===========================================
# For backtesting service
TIMERANGE=20231101-20231201
EXCHANGE=hyperliquid
PAIRS=BTC/USDC:USDC ETH/USDC:USDC SOL/USDC:USDC
TIMEFRAMES=1h 4h
DAYS=30

# ===========================================
# HYPERLIQUID SPECIFIC NOTES
# ===========================================
# 1. Hyperliquid uses USDC as the base currency (not USDT)
# 2. All pairs are futures contracts (format: BASE/USDC:USDC)
# 3. Hyperliquid is a DEX on Arbitrum One (Layer 2)
# 4. You need to deposit USDC to Hyperliquid first
# 5. Use API wallet for security (can't withdraw, only trade)
# 6. Supports stoploss_on_exchange
# 7. Limited to 5000 historical candles
# 8. No market orders (simulated with limit orders + 5% slippage)

# ===========================================
# SECURITY WARNINGS
# ===========================================
# 🚨 CRITICAL SECURITY NOTES:
# 1. NEVER commit the .env file to version control
# 2. NEVER use your main wallet private key
# 3. ALWAYS use the API wallet from Hyperliquid
# 4. Keep your private keys secure and encrypted
# 5. Start with dry_run=true for testing
# 6. Use small amounts when going live
# 7. Monitor your trades closely

# ===========================================
# SETUP INSTRUCTIONS
# ===========================================
# 1. Create API wallet at https://app.hyperliquid.xyz/API
# 2. Copy wallet address and private key above
# 3. Create Telegram bot with @BotFather
# 4. Get your Telegram chat ID
# 5. Copy this file to .env: cp .env.hyperliquid.template .env
# 6. Fill in all the values above
# 7. Start with: docker-compose -f docker-compose.telegram.yml up -d
