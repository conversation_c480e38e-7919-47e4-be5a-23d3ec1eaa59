---
services:
  freqtrade:
    # Use local build for development
    build:
      context: .
      dockerfile: "./Dockerfile"
    restart: unless-stopped
    container_name: freqtrade
    volumes:
      - "./user_data:/freqtrade/user_data"
      # Mount local strategies for development
      - "./freqtrade/strategy:/freqtrade/freqtrade/strategy"
    # Expose api on port 8080 (localhost only)
    # Please read the https://www.freqtrade.io/en/stable/rest-api/ documentation
    # for more information.
    ports:
      - "127.0.0.1:8080:8080"
    # Default command used when running `docker compose up`
    command: >
      trade
      --logfile /freqtrade/user_data/logs/freqtrade.log
      --db-url sqlite:////freqtrade/user_data/tradesv3.sqlite
      --config /freqtrade/user_data/config.json
      --strategy SampleStrategy

  # Development service for testing strategies
  freqtrade-dev:
    build:
      context: .
      dockerfile: "./Dockerfile"
    container_name: freqtrade-dev
    volumes:
      - "./user_data:/freqtrade/user_data"
      - "./freqtrade/strategy:/freqtrade/freqtrade/strategy"
      - "./tests:/freqtrade/tests"
    ports:
      - "127.0.0.1:8081:8080"
    # Override entrypoint for development
    entrypoint: ["bash"]
    command: ["-c", "sleep infinity"]
    profiles:
      - dev
