# Freqtrade Docker Development Guide

This guide shows you how to deploy your local Freqtrade codebase to Docker for development, allowing you to add custom features and strategies.

## 🚀 Quick Start

### 1. Automated Setup (Recommended)

Run the automated setup script:

```bash
./docker-dev-setup.sh
```

This will:
- ✅ Check Docker installation
- ✅ Create user_data directory structure
- ✅ Build Docker images (production & development)
- ✅ Create development docker-compose files
- ✅ Generate helper scripts
- ✅ Create sample configuration

### 2. Manual Setup (Alternative)

If you prefer manual setup:

```bash
# Build the Docker image from local source
docker build -t freqtrade-local:latest .

# Create user data directory
mkdir -p user_data/{strategies,data,logs,notebooks}

# Start with docker-compose
docker-compose up -d
```

## 🛠️ Development Workflow

### Starting Development Environment

```bash
# Start development container
./dev.sh start

# Access development shell
./dev.sh shell
```

### Creating Custom Strategies

```bash
# Create a new strategy
./dev.sh strategy MyCustomStrategy

# Edit the strategy file
# File will be created in: user_data/strategies/MyCustomStrategy.py
```

### Testing Your Changes

```bash
# Run tests
./dev.sh test

# Run specific tests
docker exec -it freqtrade-dev pytest tests/test_strategy/

# Backtest your strategy
./dev.sh backtest --strategy MyCustomStrategy --timerange 20230101-20231201
```

### Downloading Data

```bash
# Download data for backtesting
./dev.sh download --exchange binance --pairs BTC/USDT ETH/USDT --timeframes 1h 4h --days 30
```

## 📁 Directory Structure

```
freqtrade/
├── user_data/                 # Mounted volume for persistent data
│   ├── strategies/            # Your custom strategies
│   ├── data/                  # Market data
│   ├── logs/                  # Log files
│   ├── backtest_results/      # Backtest outputs
│   ├── notebooks/             # Jupyter notebooks
│   └── config.json            # Bot configuration
├── freqtrade/                 # Core freqtrade code (mounted for live editing)
├── docker-compose.yml         # Production setup
├── docker-compose.dev.yml     # Development setup
├── Dockerfile                 # Production image
├── Dockerfile.dev             # Development image
└── dev.sh                     # Development helper script
```

## 🔧 Available Commands

### Development Helper Script (`dev.sh`)

```bash
./dev.sh start      # Start development container
./dev.sh stop       # Stop development container
./dev.sh shell      # Access development shell
./dev.sh logs       # Show container logs
./dev.sh build      # Rebuild development image
./dev.sh test       # Run tests
./dev.sh backtest   # Run backtesting
./dev.sh strategy   # Create new strategy
./dev.sh download   # Download market data
```

### Direct Docker Commands

```bash
# Build from local source
docker build -t freqtrade-local .

# Run with custom strategy
docker run --rm -v $(pwd)/user_data:/freqtrade/user_data \
  freqtrade-local backtesting --strategy MyStrategy

# Access running container
docker exec -it freqtrade bash
```

## 🎯 Development Use Cases

### 1. Adding Custom Indicators

1. Edit files in `freqtrade/` directory
2. Changes are immediately reflected in the container
3. Test with: `./dev.sh test`

### 2. Creating Custom Strategies

```bash
# Create new strategy
./dev.sh strategy MyStrategy

# Edit the generated file
vim user_data/strategies/MyStrategy.py

# Test the strategy
./dev.sh backtest --strategy MyStrategy
```

### 3. Modifying Core Functionality

1. Edit core files in `freqtrade/` directory
2. Rebuild image: `./dev.sh build`
3. Test changes: `./dev.sh test`

### 4. Adding Dependencies

Edit `requirements.txt` or create `requirements-custom.txt`:

```bash
# Add to Dockerfile.dev
RUN pip install --user -r requirements-custom.txt

# Rebuild
./dev.sh build
```

## 🌐 Web UI Access

The FreqUI web interface is available at:
- **Development**: http://localhost:8080
- **Production**: http://localhost:8080

Default credentials:
- Username: `freqtrader`
- Password: `SuperSecretPassword`

## 📊 Jupyter Notebooks (Optional)

```bash
# Start Jupyter service
docker-compose -f docker-compose.dev.yml --profile jupyter up -d

# Access at: http://localhost:8888
```

## ⚙️ Configuration

### Sample Configuration

The setup creates a sample `user_data/config.json` with:
- ✅ Dry run enabled (safe for testing)
- ✅ API server enabled
- ✅ Basic exchange configuration
- ✅ Sample trading pairs

### Important Settings for Development

```json
{
  "dry_run": true,              // Keep true for development
  "api_server": {
    "enabled": true,
    "listen_ip_address": "0.0.0.0",
    "listen_port": 8080
  }
}
```

## 🔒 Security Notes

- Keep `dry_run: true` during development
- Never commit real API keys to version control
- Use environment variables for sensitive data
- The sample config uses placeholder credentials

## 🐛 Troubleshooting

### Container Won't Start
```bash
# Check logs
./dev.sh logs

# Rebuild image
./dev.sh build
```

### Permission Issues
```bash
# Fix ownership
sudo chown -R $USER:$USER user_data/
```

### Port Conflicts
```bash
# Change ports in docker-compose.dev.yml
ports:
  - "127.0.0.1:8081:8080"  # Use different port
```

## 📚 Next Steps

1. **Configure Exchange**: Update `user_data/config.json` with your exchange credentials
2. **Create Strategy**: Use `./dev.sh strategy YourStrategy` to create custom strategies
3. **Download Data**: Use `./dev.sh download` to get historical data
4. **Backtest**: Test your strategies with `./dev.sh backtest`
5. **Live Trading**: Set `dry_run: false` when ready (be careful!)

## 🤝 Contributing

When you've developed new features:

1. Test thoroughly with `./dev.sh test`
2. Create proper documentation
3. Submit pull requests to the main Freqtrade repository

Happy trading! 🚀
